const express = require('express');
const fs = require('fs');
const path = require('path');
const axios = require('axios');
const multer = require('multer');
const FormData = require('form-data');
const { checkNeedOnline } = require('./online.js');
const { processSearchData } = require('./data_search.js');
const { processThinking } = require('./thinking.js');
const { extractThinkingContent } = require('./need_thinking.js');
const { TransformerManager } = require('./content_transformers.cjs');

const app = express();

// 添加 CORS 中间件
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS, PUT, DELETE');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
  
  // 处理 OPTIONS 请求
  if (req.method === 'OPTIONS') {
    return res.sendStatus(200);
  }
  next();
});

// 增加请求体大小限制
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// 配置 multer 来处理文件上传
const upload = multer({
  storage: multer.memoryStorage(),
  limits: { fileSize: 50 * 1024 * 1024 } // 50MB limit
});

// 设置 multer
function getCurrentTimestamp() {
    return Math.floor(Date.now() / 1000);
}

const gatewayBaseUrl = 'https://gateway.588886.xyz';
const gatewayBaseUrl2 = 'https://rubeus.vpan.workers.dev'; // Secondary gateway URL
const gatewayKeys = {
  'key-1': 'a881018',
  'key-2': 'a881018dddd'
  // Add more gateway keys here
};

const confPath = '/root/test/gateway/conf.json';
const logPath = '/root/test/gateway/public/gateway.log';
const maxLogs = 1000;
const retainLogs = 900;  

function truncateBody(body) {
  const bodyStr = JSON.stringify(body);
  return bodyStr.length > 100 ? bodyStr.substring(0, 100) : bodyStr;
}

function logRequest(data) {
  const truncatedBody = truncateBody(data.body);
  const logEntry = `${data.status}|${new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })}|${data.clientIp}|${data.gatewayKey}|${data.model}|${truncatedBody}\n`;
  
  let existingLogs = '';
  if (fs.existsSync(logPath)) {
    existingLogs = fs.readFileSync(logPath, 'utf8');
  }

  // 将日志文件按换行符拆分成数组
  const logEntries = existingLogs.split('\n').filter(entry => entry.trim() !== '');

  // 如果日志条数超过 maxLogs，截取最近的 retainLogs 条
  if (logEntries.length >= maxLogs) {
    existingLogs = logEntries.slice(0, retainLogs).join('\n') + '\n'; // 只保留前 retainLogs 条
  }

  const newLogContent = logEntry + existingLogs;
  fs.writeFileSync(logPath, newLogContent);
}

// 转换旧版请求到新版格式
function transformOldRequest(requestBody) {
  if (!requestBody.functions) {
    return requestBody;
  }

  const newBody = { ...requestBody };
  delete newBody.functions;

  newBody.tools = requestBody.functions.map(func => ({
    type: 'function',
    function: {
      ...func,
      parameters: {
        ...func.parameters,
        // 如果原来没有 additionalProperties，添加 false
        additionalProperties: func.parameters.hasOwnProperty('additionalProperties') 
          ? func.parameters.additionalProperties 
          : false
      }
    },
    strict: true
  }));

  return newBody;
}

// 检查并移除 gemini 模型的不支持的参数
function removeAdditionalPropertiesForGemini(requestBody) {
  if (!requestBody.model || !requestBody.model.includes('gemini')) {
    return requestBody;
  }

  const newBody = { ...requestBody };
  
  // 删除顶层的 max_tokens
  delete newBody.max_tokens;

  if (newBody.tools) {
    newBody.tools = newBody.tools.map(tool => {
      if (tool.function && tool.function.parameters) {
        const parameters = { ...tool.function.parameters };
        
        // 删除 gemini 不支持的参数
        const unsupportedParams = [
          'additionalProperties',
          '$schema',
          'definitions',
          'default',
          'max_tokens',
          'patternProperties',
          'dependencies',
          'propertyNames',
          'if',
          'then',
          'else',
          'allOf',
          'anyOf',
          'oneOf',
          'not'
        ];
        
        unsupportedParams.forEach(param => {
          delete parameters[param];
        });

        // 递归处理嵌套的属性
        if (parameters.properties) {
          Object.keys(parameters.properties).forEach(propKey => {
            const prop = parameters.properties[propKey];
            if (typeof prop === 'object') {
              unsupportedParams.forEach(param => {
                delete prop[param];
              });
            }
          });
        }

        return {
          ...tool,
          function: {
            ...tool.function,
            parameters
          }
        };
      }
      return tool;
    });
  }
  return newBody;
}

// 转换新版响应到旧版格式
function transformNewResponse(responseData) {
  if (!responseData || !responseData.message || !responseData.message.tool_calls) {
    return responseData;
  }

  const toolCall = responseData.message.tool_calls[0];
  return {
    index: responseData.index,
    logprobs: responseData.logprobs,
    finish_reason: "function_call",
    message: {
      role: responseData.message.role,
      content: null,
      function_call: {
        name: toolCall.function.name,
        arguments: toolCall.function.arguments
      }
    }
  };
}

// 生成随机工具调用ID
function generateToolCallId() {
  return Math.random().toString(36).substring(2, 12);
}

// 获取当前北京时间的格式化字符串
function getCurrentBeijingTime() {
  return new Date().toLocaleString('zh-CN', {
    timeZone: 'Asia/Shanghai',
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  });
}

// 启动预处理心跳机制
function startPreprocessingHeartbeat(res, model, stage) {
  console.log(`[${new Date().toISOString()}] Starting preprocessing heartbeat for ${model} - Stage: ${stage}`);

  // 如果还没有发送响应头，先发送
  if (!res.headersSent) {
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control'
    });
  }

  // 每3秒发送一次预处理心跳
  const heartbeatInterval = setInterval(() => {
    if (!res.writableEnded) {
      try {
        const heartbeatData = {
          choices: [{
            delta: { content: "" },
            index: 0,
            finish_reason: null
          }],
          created: Math.floor(Date.now() / 1000),
          id: `chatcmpl-preprocessing-${stage}-${Date.now()}`,
          model: model,
          object: "chat.completion.chunk"
        };
        res.write(`data: ${JSON.stringify(heartbeatData)}\n\n`);
        console.log(`[${new Date().toISOString()}] Sent preprocessing heartbeat for ${stage}`);
      } catch (err) {
        console.error(`[${new Date().toISOString()}] Error sending preprocessing heartbeat:`, err);
        clearInterval(heartbeatInterval);
      }
    } else {
      clearInterval(heartbeatInterval);
    }
  }, 3000);

  return heartbeatInterval;
}

// 处理日志中的base64内容，避免控制台被刷满
function truncateBase64Content(obj) {
  if (!obj) return obj;
  
  // 如果是字符串，检查是否为base64编码
  if (typeof obj === 'string') {
    // 检测常见的base64图片格式
    if (obj.match(/^data:image\/[a-z]+;base64,/i) || 
        (obj.match(/^([A-Za-z0-9+/]{4})*([A-Za-z0-9+/]{3}=|[A-Za-z0-9+/]{2}==)?$/) && obj.length > 50000)) {
      // 只保留前20个字符和标识
      return obj.substring(0, 20) + "... [base64内容已省略，长度: " + obj.length + "]";
    }
    return obj;
  }
  
  // 如果是数组，递归处理
  if (Array.isArray(obj)) {
    return obj.map(item => truncateBase64Content(item));
  }
  
  // 如果是对象，递归处理
  if (typeof obj === 'object') {
    const result = {};
    for (const [key, value] of Object.entries(obj)) {
      result[key] = truncateBase64Content(value);
      
      // 特别处理可能包含base64内容的字段
      if (['image', 'content', 'file', 'data', 'media'].includes(key) && 
          typeof value === 'string' && 
          value.length > 50000) {
        result[key] = value.substring(0, 20) + "... [可能的base64内容已省略，长度: " + value.length + "]";
      }
    }
    return result;
  }
  
  return obj;
}

// 转换消息数组
function transformMessages(messages) {
  if (!Array.isArray(messages)) {
    return messages;
  }

  // 用于存储 function_call 和对应的 tool_call_id 的映射
  const functionCallIds = new Map();

  return messages.map(message => {
    // 如果消息中没有 function_call，保持原样返回
    if (!message.function_call && message.role !== 'function') {
      return message;
    }

    // 处理 assistant 角色的消息
    if (message.role === 'assistant' && message.function_call) {
      const toolCallId = generateToolCallId();
      // 存储 function_call 的 name 和生成的 id 的映射
      functionCallIds.set(message.function_call.name, toolCallId);

      return {
        role: 'assistant',
        tool_calls: [{
          id: toolCallId,
          type: 'function',
          function: {
            name: message.function_call.name,
            arguments: message.function_call.arguments
          }
        }]
      };
    }

    // 处理 function 角色的消息
    if (message.role === 'function') {
      const toolCallId = functionCallIds.get(message.name) || generateToolCallId();
      return {
        role: 'tool',
        tool_call_id: toolCallId,
        name: message.name,
        content: message.content
      };
    }

    return message;
  });
}

// 在 transformMessages 函数前添加新函数
function processGrokMessages(messages) {
  if (!Array.isArray(messages)) {
    return messages;
  }

  return messages.map(message => {
    // 如果消息中没有 content 或 content 为空，添加默认值
    if (!message.content || message.content.trim() === '') {
      return {
        ...message,
        content: 'ok'
      };
    }
    return message;
  });
}

// 转换旧版请求到新版格式
function transformOldRequest(requestBody) {
  if (!requestBody.functions) {
    return requestBody;
  }

  const newBody = { ...requestBody };
  
  // 转换 functions 到 tools
  newBody.tools = requestBody.functions.map(func => ({
    type: 'function',
    function: {
      ...func,
      parameters: {
        ...func.parameters,
        additionalProperties: func.parameters.hasOwnProperty('additionalProperties') 
          ? func.parameters.additionalProperties 
          : false
      }
    },
    strict: true
  }));
  delete newBody.functions;

  // 转换消息数组
  if (newBody.messages) {
    newBody.messages = transformMessages(newBody.messages);
  }

  return newBody;
}

// 检查是否需要转换响应
function shouldTransformResponse(req, responseData) {
  return req.body.functions !== undefined && 
         responseData.message?.tool_calls && 
         responseData.message.tool_calls.length > 0;
}

// 简单版本的处理逻辑（用于v2路线）
function createSimpleGatewayHandler(gatewayPath) {
  return async (req, res) => {
    const authHeader = req.headers['authorization'];
    const clientIp = req.headers['x-forwarded-for'] || req.ip;

    // 验证授权
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const token = authHeader.split(' ')[1];
    const gatewayKeyName = Object.keys(gatewayKeys).find(key => gatewayKeys[key] === token);

    if (!gatewayKeyName) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // 处理模型列表请求
    if (req.path === `/${gatewayPath}/v2/models`) {
      try {
        const confContent = fs.readFileSync(confPath, 'utf8');
        const jsonData = JSON.parse(confContent);

        // 提取 models 部分
        const models = jsonData.models || {};
        const currentTime = getCurrentTimestamp();

        // 格式化模型数据，根据 use-in 参数和 type 过滤
        const formattedModels = Object.entries(models)
          .filter(([_, modelConfig]) => {
            // 检查是否是新格式
            if (typeof modelConfig === 'object') {
              // 检查 use-in 参数是否包含当前路径
              const useIn = modelConfig['use-in'] || ['gateway']; // 默认为 gateway
              const pathMatches = useIn.includes(gatewayPath);

              // 检查 type 是否包含 chat
              const typeMatches = modelConfig.type ? modelConfig.type.includes('chat') : true;

              return pathMatches && typeMatches;
            }
            // 旧格式全部保留（为了向后兼容）
            return true;
          })
          .map(([modelName, _]) => ({
            id: modelName,
            object: 'model',
            created: currentTime,
            owned_by: 'system'
          }));

        // 返回模型信息
        return res.json({ data: formattedModels });
      } catch (error) {
        console.error('Error reading or parsing config file:', error);
        return res.status(500).json({ error: 'Failed to read configuration file' });
      }
    }

    // 处理聊天完成请求
    const model = req.body.model;

    if (!model) {
      return res.status(400).json({ error: 'Model is required' });
    }

    console.log(`[V2] Request Key: ${gatewayKeyName}, Request model: ${model}, From: ${clientIp}`);

    let config;
    let mappedModel = model;

    try {
      const confContent = fs.readFileSync(confPath, 'utf8');
      const conf = JSON.parse(confContent);

      // 检查模型映射
      if (conf.modelsmap && conf.modelsmap[model]) {
        mappedModel = conf.modelsmap[model];
        console.log(`[V2] Mapped model to: ${mappedModel}`);
      }

      // 获取模型配置
      const modelConfig = conf.models[model];
      if (!modelConfig) {
        return res.status(503).json({ error: 'Model not registered' });
      }

      const configName = modelConfig["config-name"] || modelConfig;
      if (!configName) {
        return res.status(503).json({ error: 'Model configuration not found' });
      }

      config = conf.configurations[configName];
      if (!config) {
        return res.status(503).json({ error: 'Configuration not found' });
      }

      // 解析配置引用
      if (typeof config === 'string') {
        config = conf.providers[config];
      }

      // 递归解析 targets - 完全按照原始代码的逻辑
      const resolveTargets = (targetsOrProvider) => {
        if (typeof targetsOrProvider === 'string' && conf.providers[targetsOrProvider]) {
          return conf.providers[targetsOrProvider];  // 解析直接引用的 provider
        }

        if (Array.isArray(targetsOrProvider)) {
          return targetsOrProvider.map(target => {
            if (typeof target === 'string' && conf.providers[target]) {
              return conf.providers[target]; // 解析数组中的 provider 引用
            } else if (target.base_provider && conf.providers[target.base_provider]) {
              // 如果有 base_provider，则获取基础 provider 配置，并与 added_params 合并
              let resolvedProvider = Object.assign({}, conf.providers[target.base_provider], target.added_params);
              return resolvedProvider;
            } else if (target.targets) {
              target.targets = resolveTargets(target.targets);  // 递归处理嵌套的 targets
            }
            return target;
          });
        }

        // 处理对象形式的 base_provider 配置
        if (targetsOrProvider.base_provider && conf.providers[targetsOrProvider.base_provider]) {
          // 合并 base_provider 和 added_params
          return Object.assign({}, conf.providers[targetsOrProvider.base_provider], targetsOrProvider.added_params);
        }

        return targetsOrProvider; // 返回原始配置（如果没有需要解析的部分）
      };

      // 合并 base_provider 和 config 中的额外参数
      const mergeProviderConfig = (baseProviderKey, additionalConfig) => {
        if (!conf.providers[baseProviderKey]) {
          return additionalConfig;
        }

        // 创建基础配置的副本
        const result = { ...conf.providers[baseProviderKey] };

        // 创建additionalConfig的副本，以免修改原始对象
        const configCopy = { ...additionalConfig };

        // 如果存在added_params，将其内容复制到顶层
        if (configCopy.added_params) {
          // 将added_params中的所有内容复制到顶层
          Object.assign(result, configCopy.added_params);
        }

        // 最后合并剩余的配置（包括保留原始的added_params）
        Object.assign(result, configCopy);

        return result;
      };

      // 检查 config，如果是字符串（如 common-config-10），则解析 provider
      if (typeof config === 'string') {
        config = conf.providers[config]; // 直接将 provider 名称替换为完整的配置
      }

      // 如果有 base_provider 则进行合并
      if (config.base_provider) {
        config = mergeProviderConfig(config.base_provider, config);
      }

      // 处理 targets（如果有）
      if (config.targets) {
        config.targets = resolveTargets(config.targets);
      }

      // 配置解析完成
      console.log(`[V2] Configuration resolved for model: ${model}`);

    } catch (err) {
      console.error('[V2] Error reading or parsing config file:', err);
      return res.status(500).json({ error: 'Internal server error' });
    }

    // 构建请求
    const requestPath = req.path.replace(`/${gatewayPath}`, '');
    const gatewayUrl = `${gatewayBaseUrl}${requestPath}`;

    console.log(`[V2] Gateway request URL: ${gatewayUrl}`);

    try {
      const requestData = { ...req.body, model: mappedModel };

      const headers = {
        'x-portkey-config': JSON.stringify(config),
        'Content-Type': 'application/json'
      };

      console.log(`[V2] Sending request to gateway for model: ${mappedModel}`);

      const response = await axios({
        method: req.method,
        url: gatewayUrl,
        headers: headers,
        data: requestData,
        responseType: 'stream',
        timeout: 120000,
        validateStatus: function (status) {
          return status < 600;
        }
      });

      console.log(`[V2] Response received - Status: ${response.status}`);

      // 设置响应头
      if (!res.headersSent) {
        res.writeHead(response.status, response.headers);
      }

      // 转发响应流
      response.data.pipe(res);

    } catch (error) {
      console.error('[V2] Error forwarding request:', error);
      if (!res.headersSent) {
        res.status(500).json({ error: 'Gateway request failed' });
      }
    }
  };
}

// 复杂版本的处理逻辑（用于v1路线）
function createGatewayHandler(gatewayPath) {
  return async (req, res) => {
    const authHeader = req.headers['authorization'];
    const clientIp = req.headers['x-forwarded-for'] || req.ip;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const token = authHeader.split(' ')[1];
    const gatewayKeyName = Object.keys(gatewayKeys).find(key => gatewayKeys[key] === token);

    if (!gatewayKeyName) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // 检查路径是否为 /{gatewayPath}/v1/models
    if (req.path === `/${gatewayPath}/v1/models`) {
        const filePath = path.join('/root/test/gateway/conf.json'); // 根据你的实际文件路径调整

        // 读取 conf.json 文件
        fs.readFile(filePath, 'utf8', (err, data) => {
            if (err) {
                console.error('Error reading conf.json:', err);
                return res.status(500).json({ error: 'Failed to read configuration file' });
            }

            try {
                const jsonData = JSON.parse(data);

                // 提取 models 部分
                const models = jsonData.models || {};
                const currentTime = getCurrentTimestamp();

                // 格式化模型数据，根据 use-in 参数和 type 过滤
                const formattedModels = Object.entries(models)
                    .filter(([_, modelConfig]) => {
                        // 检查是否是新格式
                        if (typeof modelConfig === 'object') {
                            // 检查 use-in 参数是否包含当前路径
                            const useIn = modelConfig['use-in'] || ['gateway']; // 默认为 gateway
                            const pathMatches = useIn.includes(gatewayPath);

                            // 检查 type 是否包含 chat
                            const typeMatches = modelConfig.type ? modelConfig.type.includes('chat') : true;

                            return pathMatches && typeMatches;
                        }
                        // 旧格式全部保留（为了向后兼容）
                        return true;
                    })
                    .map(([modelName, _]) => ({
                        id: modelName,
                        object: 'model',
                        created: currentTime,
                        owned_by: 'system'
                    }));

                // 返回模型信息
                return res.json({ data: formattedModels });
            } catch (parseError) {
                console.error('Error parsing JSON:', parseError);
                return res.status(500).json({ error: 'Failed to parse configuration file' });
            }
        });
    } else {
        // 非 /{gatewayPath}/v1/models 的请求，继续执行原来的逻辑
  let model = req.body.model;
  
   // 用于测试不同模型提交进来的请求内容
 // console.log("req.body:", JSON.stringify(req.body, null, 2));
  
  // 如果在 body 中没有找到 model，尝试从 fields 中获取
  if (!model && req.files) {
    const modelField = req.files.find(file => file.fieldname === 'model');
    if (modelField) {
      model = modelField.buffer.toString();
    }
  }
  
  let originalModel = req.body.model;
  
  // 如果在 body 中没有找到 model，尝试从 fields 中获取
  if (!originalModel && req.files) {
    const modelField = req.files.find(file => file.fieldname === 'model');
    if (modelField) {
      originalModel = modelField.buffer.toString();
    }
  }

  console.log(`Request Key: ${gatewayKeyName}, Request model: ${originalModel}, From: ${clientIp}`);

  let isThinkingMode = false; // 移到这里，作为全局标志
  let isSearchMode = false; // 添加搜索模式标志

  // 首先检查是否通过 model 前缀进入思考模式
  if (req.body.model && (req.body.model.startsWith('r1with-') || req.body.model.startsWith('r1only-'))) {
      // 启动 r1 模式预处理心跳
      let r1Heartbeat;
      if (req.body.stream !== false) {
          r1Heartbeat = startPreprocessingHeartbeat(res, req.body.model, 'r1-conversion');
      }

      try {
          console.log(`[${new Date().toISOString()}] Starting R1 mode preprocessing for model: ${req.body.model}`);

          // 确定是哪种模式
          const isR1Only = req.body.model.startsWith('r1only-');
          const prefix = isR1Only ? 'r1only-' : 'r1with-';
          
          // 从 model 字符串中提取 r1model 和 model
          const modelStr = req.body.model.substring(prefix.length);
          
          if (isR1Only) {
              // r1only 模式只需要 r1model
              const r1model = modelStr;
              console.log(`R1Only mode - r1model: ${r1model}`);
              
              // 更新 originalModel 和 model 变量
              originalModel = r1model;
              finalModel = r1model;
              
              // 添加新的思考模式标记
              let newStopArray = [];
              
              // 如果原来有 stop 序列，保留非 thinking 相关的内容
              if (req.body.stop && Array.isArray(req.body.stop)) {
                  newStopArray = req.body.stop.filter(stop => 
                      !stop.startsWith('thinking') && !stop.startsWith('force-thinking')
                  );
              }
              
              // 添加 force-thinking 标记，但不要添加额外的后缀
              // 检查 r1model 是否已经包含 -deepseek-r1 后缀
              const suffix = r1model.includes('-deepseek-r1') 
                  ? r1model.replace('-deepseek-r1', '') 
                  : r1model;
              
              //newStopArray.push(`force-thinking-${suffix}`);
              newStopArray.push(`thinking-${suffix}`);
              
              req.body = {
                  ...req.body,
                  model: r1model,
                  stop: newStopArray,
                  r1only: true  // 添加标记表示这是 r1only 模式
              };
              
              console.log('Converted r1only request to stop sequence format');
          } else {
              // 原有的 r1with 模式处理
              const [r1model, model] = modelStr.split('@');
              
              if (!r1model || !model) {
                  throw new Error('Invalid model format. Expected: r1with-{r1model}@{model}');
              }

              console.log(`Extracted models - r1model: ${r1model}, model: ${model}`);
              
              // 将请求转换为使用 stop 序列的方式
              const suffix = r1model.replace('-deepseek-r1', '');
              
              // 更新 originalModel 和 model 变量，这很重要
              originalModel = model;
              finalModel = model;
              
              // 处理 stop 序列
              let newStopArray = [];
              
              // 如果原来有 stop 序列，保留非 thinking 相关的内容
              if (req.body.stop && Array.isArray(req.body.stop)) {
                  newStopArray = req.body.stop.filter(stop => 
                      !stop.startsWith('thinking') && !stop.startsWith('force-thinking')
                  );
              }
              
              // 添加新的思考模式标记
              //newStopArray.push(`force-thinking-${suffix}`);
              newStopArray.push(`thinking-${suffix}`);
              
              req.body = {
                  ...req.body,
                  model: model,  // 使用最终要用的模型
                  stop: newStopArray  // 使用处理后的 stop 序列
              };
              
              console.log('Converted r1with request to stop sequence format');
          }

          console.log(`[${new Date().toISOString()}] R1 mode preprocessing completed`);
      } catch (error) {
          console.error('Error converting r1 request:', error);
          console.log('Continuing with original request due to error');
      } finally {
          // 清理 r1 模式心跳
          if (r1Heartbeat) {
              clearInterval(r1Heartbeat);
              console.log(`[${new Date().toISOString()}] Cleared R1 preprocessing heartbeat`);
          }
      }
  }

  // 设置思考模式标志
  isThinkingMode = req.body.stop && Array.isArray(req.body.stop) &&
      req.body.stop.some(stop => stop.startsWith('thinking') || stop.startsWith('force-thinking'));

  // 设置搜索模式标志
  isSearchMode = req.body.stop && Array.isArray(req.body.stop) &&
      (req.body.stop.includes('basic') ||
       req.body.stop.includes('detail') ||
       req.body.stop.includes('perplexity-basic') ||
       req.body.stop.includes('perplexity-detail') ||
       req.body.stop.includes('metaso-basic') ||
       req.body.stop.includes('metaso-detail') ||
       req.body.stop.includes('kimi-basic') ||
       req.body.stop.includes('kimi-detail') ||
       req.body.stop.includes('detail-exa') ||
       req.body.stop.includes('detail-tav') ||
       req.body.stop.includes('detail-serper') ||
       req.body.stop.includes('detail-serpapi'));

  // 处理搜索和思考模式
  if (!req.body.model?.startsWith('r1with-') && !req.body.model?.startsWith('r1only-')) {
      // 检查是否需要处理搜索
      if (req.body.stop && Array.isArray(req.body.stop) &&
          (req.body.stop.includes('basic') ||
           req.body.stop.includes('detail') ||
           req.body.stop.includes('perplexity-basic') ||
           req.body.stop.includes('perplexity-detail') ||
           req.body.stop.includes('metaso-basic') ||
           req.body.stop.includes('metaso-detail') ||
           req.body.stop.includes('kimi-basic') ||
           req.body.stop.includes('kimi-detail') ||
           req.body.stop.includes('detail-exa') ||
           req.body.stop.includes('detail-tav') ||
           req.body.stop.includes('detail-serper') ||
           req.body.stop.includes('detail-serpapi'))) {

          // 启动搜索预处理心跳
          let searchHeartbeat;
          if (req.body.stream !== false) {
              searchHeartbeat = startPreprocessingHeartbeat(res, originalModel, 'search');
          }

          try {
              console.log(`[${new Date().toISOString()}] Starting search preprocessing for model: ${originalModel}`);

              // 检查是否需要在线搜索
              const checkResult = await checkNeedOnline(req.body.messages);
              console.log(checkResult);
              if (checkResult.need_online) {
                  // 在流式模式下发送搜索开始状态
                  if (req.body.stream !== false && res) {
                      if (!res.headersSent) {
                          res.writeHead(200, {
                              'Content-Type': 'text/event-stream',
                              'Cache-Control': 'no-cache',
                              'Connection': 'keep-alive',
                              'Access-Control-Allow-Origin': '*',
                              'Access-Control-Allow-Headers': 'Cache-Control'
                          });
                      }
                      
                      // 发送搜索开始的流式块
                      const searchStartChunk = {
                          choices: [{
                              delta: { content: "<think>" },
                              index: 0,
                              finish_reason: null
                          }],
                          created: Math.floor(Date.now() / 1000),
                          id: `chatcmpl-search-start-${Date.now()}`,
                          model: originalModel,
                          object: "chat.completion.chunk"
                      };
                      res.write(`data: ${JSON.stringify(searchStartChunk)}\n\n`);
                      console.log(`[${new Date().toISOString()}] Sent search start notification to client`);
                  }

                  // 需要在线搜索，处理搜索请求
                  const searchQuery = req.body.stop.some(stop =>
                      stop === 'perplexity-basic' ||
                      stop === 'perplexity-detail' ||
                      stop === 'metaso-basic' ||
                      stop === 'metaso-detail' ||
                      stop === 'kimi-basic' ||
                      stop === 'kimi-detail' ||
                      stop === 'detail-exa' ||
                      stop === 'detail-tav' ||
                      stop === 'detail-serper' ||
                      stop === 'detail-serpapi'
                  ) ? checkResult.question : checkResult.query;

                  // 获取当前北京时间，并添加到搜索查询中
                  const currentTime = getCurrentBeijingTime();
                  console.log(`添加时间到搜索查询: ${currentTime}`);
                  const processedData = await processSearchData(req.body, searchQuery, currentTime, res);
                  req.body = processedData;

                  // 在流式模式下发送搜索结束状态
                  if (req.body.stream !== false && res && !res.writableEnded) {
                      const searchEndChunk = {
                          choices: [{
                              delta: { content: "</think>\n检索完成，AI阅读中~~~" },
                              index: 0,
                              finish_reason: null
                          }],
                          created: Math.floor(Date.now() / 1000),
                          id: `chatcmpl-search-end-${Date.now()}`,
                          model: originalModel,
                          object: "chat.completion.chunk"
                      };
                      res.write(`data: ${JSON.stringify(searchEndChunk)}\n\n`);
                      console.log(`[${new Date().toISOString()}] Sent search end notification to client`);
                  }

                  console.log(`[${new Date().toISOString()}] Search preprocessing completed`);
              } else {
                  // 不需要在线搜索，保留 stop 参数以便后续处理
                  console.log('不需要在线搜索');
              }
          } catch (error) {
              console.error('Error processing search request:', error);
              console.log('Continuing with original request due to error');
          } finally {
              // 清理搜索心跳
              if (searchHeartbeat) {
                  clearInterval(searchHeartbeat);
                  console.log(`[${new Date().toISOString()}] Cleared search preprocessing heartbeat`);
              }
          }
      }

      // 检查是否需要处理思考模式
      if (req.body.stop && Array.isArray(req.body.stop)) {
          // 检查是否包含思考模式标记
          const thinkingStop = req.body.stop.find(stop => 
              stop.startsWith('thinking') || stop.startsWith('force-thinking')
          );

          if (thinkingStop) {
              // 启动思考预处理心跳
              let thinkingHeartbeat;
              if (req.body.stream !== false) {
                  thinkingHeartbeat = startPreprocessingHeartbeat(res, originalModel, 'thinking');
              }

              try {
                  console.log(`[${new Date().toISOString()}] Starting thinking preprocessing for model: ${originalModel}`);

                  let thinkingContent;

                  // 检查是否是 force-thinking 模式
                  const isForceThinking = thinkingStop.startsWith('force-thinking');
                  
                  if (isForceThinking) {
                      // force-thinking 模式直接设置空值，跳过 extractThinkingContent
                      thinkingContent = '';
                      console.log('Force thinking mode detected, skipping content extraction');
                  } else if (req.body.r1only) {
                      // r1only 模式的原有逻辑
                      const messages = req.body.messages;
                      console.log('\n[R1Only Debug] Received messages:', JSON.stringify(truncateBase64Content(messages), null, 2));
                      
                      const lastUserMessage = messages[messages.length - 1];
                      console.log('\n[R1Only Debug] Last message:', JSON.stringify(truncateBase64Content(lastUserMessage), null, 2));
                      
                      // 从最后一条消息向上查找最近的 tool 响应
                      let toolContent = '';
                      let searchCount = 0;
                      console.log('\n[R1Only Debug] Searching for tool response...');
                      
                      for (let i = messages.length - 1; i >= 0 && searchCount < 5; i--) {
                          const msg = messages[i];
                          console.log(`\n[R1Only Debug] Checking message ${i}:`, JSON.stringify(truncateBase64Content(msg), null, 2));
                          
                          if (msg.role === 'tool') {
                              toolContent = msg.content;
                              console.log('\n[R1Only Debug] Found tool content:', truncateBase64Content(toolContent));
                              break;
                          }
                          if (msg.role === 'user') {
                              searchCount++;
                              console.log('\n[R1Only Debug] User message count:', searchCount);
                          }
                      }
                        // 获取当前北京时间
                      const currentTime = getCurrentBeijingTime();
                      
                      // 组合内容，添加当前时间
                      thinkingContent = toolContent 
                          ? `${truncateBase64Content(lastUserMessage).content}，当前时间：${currentTime}，可参考资料："${truncateBase64Content(toolContent)}"`
                          : `${truncateBase64Content(lastUserMessage).content}，当前时间：${currentTime}`;

                      console.log('\n[R1Only Debug] Final thinkingContent:', truncateBase64Content(thinkingContent));
                  } else {
                      // 普通 thinking 模式的原有逻辑
                      console.log('Extracting thinking content...');
                      thinkingContent = await extractThinkingContent(req.body.messages);
                      
                      // 获取当前北京时间并添加到思考内容中
                      const currentTime = getCurrentBeijingTime();
                      thinkingContent = `${thinkingContent}，当前时间：${currentTime}`;
                      
                      console.log('Thinking content extracted with time:', truncateBase64Content(thinkingContent));
                  }

                  // 不论是哪种模式，都继续处理
                  console.log('Processing thinking mode...');
                  
                  // 确定使用的 r1model
                  let r1model = "deepseek-r1-stable"; // 默认值
                  
                  // 检查是否有后缀
                  const match = thinkingStop.match(/(?:thinking|force-thinking)-(.+)/);
                  if (match) {
                      // 如果有后缀，使用 后缀-deepseek-r1 作为 r1model
                      r1model = `${match[1]}-deepseek-r1`;
                  }
                  
                  console.log(`Using r1model: ${r1model}`);
                  
                  // 使用新的参数调用 processThinking
                  const processedData = await processThinking(
                      req.body, 
                      thinkingContent, 
                      res,
                      req.body.stream !== false, // 默认为 true
                      r1model
                  );

                  // 检查是否为 r1only 模式
                  if (processedData.r1only) {
                      console.log('R1Only 模式完成，不需要进一步处理');
                      // 清理思考心跳
                      if (thinkingHeartbeat) {
                          clearInterval(thinkingHeartbeat);
                          console.log(`[${new Date().toISOString()}] Cleared thinking preprocessing heartbeat (r1only)`);
                      }
                      return; // 直接返回，不继续处理
                  }

                  req.body = processedData;
                  console.log(`[${new Date().toISOString()}] Thinking preprocessing completed`);
              } catch (error) {
                  console.error('Error processing thinking request:', error);
                  console.log('Continuing with original request due to error');
              } finally {
                  // 清理思考心跳
                  if (thinkingHeartbeat) {
                      clearInterval(thinkingHeartbeat);
                      console.log(`[${new Date().toISOString()}] Cleared thinking preprocessing heartbeat`);
                  }
              }
          } else {
              delete req.body.stop;
              console.log('不需要思考');
          }
      }
  }
  
  let config;
  let mappedModel = originalModel;  // 使用更新后的 originalModel
  let transformerTypes = []; // 存储需要启用的转换器类型
  try {
    const confContent = fs.readFileSync(confPath, 'utf8');
    const conf = JSON.parse(confContent);

    if (conf.modelsmap && conf.modelsmap[originalModel]) {
        mappedModel = conf.modelsmap[originalModel];
        console.log(`Mapped model to: ${mappedModel}`);
        // 更新请求体中的 model
        req.body.model = mappedModel;
    }

    const commonConfigKey = conf.models[originalModel]?.["config-name"] || conf.models[originalModel];

    if (!commonConfigKey) {
        return res.status(503).json({ error: 'Model not registered' });
    }

    config = conf.configurations[commonConfigKey];

    if (!config) {
        return res.status(503).json({ error: 'Model not registered' });
    }

  // 转换器类型将在解析配置时动态获取
  // transformerTypes 将在 resolveConfig 函数中设置  // 处理单个 provider 或 targets 数组
  const resolveTargets = (targetsOrProvider) => {
    if (typeof targetsOrProvider === 'string' && conf.providers[targetsOrProvider]) {
      return conf.providers[targetsOrProvider];  // 解析直接引用的 provider
    }

    if (Array.isArray(targetsOrProvider)) {
      return targetsOrProvider.map(target => {
        if (typeof target === 'string' && conf.providers[target]) {
          return conf.providers[target]; // 解析数组中的 provider 引用
        } else if (target.base_provider && conf.providers[target.base_provider]) {
          // 如果有 base_provider，则获取基础 provider 配置，并与 added_params 合并
          let resolvedProvider = Object.assign({}, conf.providers[target.base_provider], target.added_params);
          return resolvedProvider;
        } else if (target.targets) {
          target.targets = resolveTargets(target.targets);  // 递归处理嵌套的 targets
        }
        return target;
      });
    }

    // 处理对象形式的 base_provider 配置
    if (targetsOrProvider.base_provider && conf.providers[targetsOrProvider.base_provider]) {
      // 合并 base_provider 和 added_params
      return Object.assign({}, conf.providers[targetsOrProvider.base_provider], targetsOrProvider.added_params);
    }

    return targetsOrProvider; // 返回原始配置（如果没有需要解析的部分）
  };

  // 合并 base_provider 和 config 中的额外参数
  const mergeProviderConfig = (baseProviderKey, additionalConfig) => {
    if (!conf.providers[baseProviderKey]) {
      return additionalConfig;
    }

    // 创建基础配置的副本
    const result = { ...conf.providers[baseProviderKey] };
    
    // 创建additionalConfig的副本，以免修改原始对象
    const configCopy = { ...additionalConfig };
    
    // 如果存在added_params，将其内容复制到顶层
    if (configCopy.added_params) {
      // 将added_params中的所有内容复制到顶层
      Object.assign(result, configCopy.added_params);
    }
    
    // 最后合并剩余的配置（包括保留原始的added_params）
    Object.assign(result, configCopy);
    
    return result;
  };

  // 检查 config，如果是字符串（如 common-config-10），则解析 provider
  if (typeof config === 'string') {
    config = conf.providers[config]; // 直接将 provider 名称替换为完整的配置
  }

  // 如果有 base_provider 则进行合并
  if (config.base_provider) {
    config = mergeProviderConfig(config.base_provider, config);
  }

  // 处理 targets（如果有）
  if (config.targets) {
    config.targets = resolveTargets(config.targets);
  }

  //console.log('Resolved Configuration:', config);
  
  // 先不收集转换器类型，等到知道使用的具体target后再决定
  // 但我们需要为每个target标记其转换器信息，用于后续匹配
  const addTargetIds = (configObj) => {
    if (!configObj || typeof configObj !== 'object') return configObj;
    
    if (Array.isArray(configObj.targets)) {
      configObj.targets = configObj.targets.map((target, index) => {
        const targetWithId = { ...target, __target_index: index };
        
        // 如果该target有转换器配置，记录下来
        if (target.added_params && target.added_params['trans-type']) {
          targetWithId.__trans_types = target.added_params['trans-type'];
        }
        
        return targetWithId;
      });
    }
    
    return configObj;
  };
  
  // 为config添加target标识
  config = addTargetIds(config);
  
  // 暂时不初始化转换器，等响应时根据实际使用的target再决定
  let transformerTypes = [];
   
  } catch (err) {
    console.error('Error reading or parsing config file', err);
    return res.status(500).json({ error: 'Internal server error' });
  }

  const requestPath = req.path.replace(`/${gatewayPath}`, '');
  let gatewayUrl = `${gatewayBaseUrl}${requestPath}`;
  console.log(`Gateway request URL: ${gatewayUrl}`);
  //console.log('x-portkey-config: ', config);

  const makeRequest = async (url) => {
    const requestStartTime = Date.now();
    const isGeminiModel = mappedModel && mappedModel.toLowerCase().includes('gemini');

    console.log(`[${new Date().toISOString()}] Starting request for model: ${mappedModel}, isGemini: ${isGeminiModel}`);
    console.log(`[${new Date().toISOString()}] Request URL: ${url}`);

    try {
      let data;
      let headers = {
        'x-portkey-config': JSON.stringify(config),
      };

      if (req.is('multipart/form-data')) {
        // 处理 multipart/form-data 请求
        const formData = new FormData();
        
        // 添加所有字段到 FormData，使用映射后的 model
        for (const [key, value] of Object.entries(req.body)) {
          if (key === 'model') {
            formData.append(key, mappedModel);
          } else if (Array.isArray(value)) {
            value.forEach((item, index) => {
              formData.append(`${key}[${index}]`, item);
            });
          } else {
            formData.append(key, value);
          }
        }
        
        // 添加所有文件到 FormData
        if (req.files) {
          req.files.forEach(file => {
            formData.append(file.fieldname, file.buffer, {
              filename: file.originalname,
              contentType: file.mimetype
            });
          });
        }

        data = formData;
        headers = { ...headers, ...formData.getHeaders() };
      } else {
        // 处理 JSON 请求
        let transformedBody = { ...req.body, model: mappedModel };
        
        // 检查是否需要转换旧版请求格式
        if (transformedBody.functions) {
          transformedBody = transformOldRequest(transformedBody);
        }
        
        // 处理 gemini 模型的特殊情况
        transformedBody = removeAdditionalPropertiesForGemini(transformedBody);
        
        // 添加 grok 模型的特殊处理
        if (transformedBody.model && transformedBody.model.toLowerCase().includes('grok')) {
          if (transformedBody.messages) {
            transformedBody.messages = processGrokMessages(transformedBody.messages);
          }
        }

        data = transformedBody;
        headers['Content-Type'] = 'application/json';
      }

      console.log("tinyed-data:", JSON.stringify(truncateBase64Content(data), null, 2));
      console.log(`[${new Date().toISOString()}] Sending axios request...`);

      // 为所有流式请求在等待响应期间发送心跳
      let waitingHeartbeat;
      if (req.body.stream !== false) {
        console.log(`[${new Date().toISOString()}] Setting up waiting heartbeat for model: ${mappedModel}`);

        // 立即设置响应头并开始发送心跳
        if (!res.headersSent) {
          res.writeHead(200, {
            'Content-Type': 'text/event-stream',
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Cache-Control'
          });
        }

        // 每5秒发送一次等待心跳
        waitingHeartbeat = setInterval(() => {
          if (!res.writableEnded) {
            try {
              const waitingData = {
                choices: [{
                  delta: { content: "" },
                  index: 0,
                  finish_reason: null
                }],
                created: Math.floor(Date.now() / 1000),
                id: `chatcmpl-waiting-${Date.now()}`,
                model: mappedModel,
                object: "chat.completion.chunk"
              };
              res.write(`data: ${JSON.stringify(waitingData)}\n\n`);
              console.log(`[${new Date().toISOString()}] Sent waiting heartbeat for ${mappedModel}`);
            } catch (err) {
              console.error(`[${new Date().toISOString()}] Error sending waiting heartbeat:`, err);
              clearInterval(waitingHeartbeat);
            }
          } else {
            clearInterval(waitingHeartbeat);
          }
        }, 5000);
      }

      const response = await axios({
        method: req.method,
        url: url,
        headers: headers,
        data: data,
        responseType: 'stream',
        timeout: 120000, // 120秒超时，与test.js保持一致
        // 添加请求和响应拦截器来处理超时
        validateStatus: function (status) {
          return status < 600; // 接受所有小于600的状态码
        }
      });

      // 收到响应后清除等待心跳
      if (waitingHeartbeat) {
        clearInterval(waitingHeartbeat);
        console.log(`[${new Date().toISOString()}] Cleared waiting heartbeat`);
      }

      const responseTime = Date.now() - requestStartTime;
      console.log(`[${new Date().toISOString()}] Response received - Status: ${response.status}, Time: ${responseTime}ms`);
      console.log(`[${new Date().toISOString()}] Response headers:`, response.headers);
      
      // 如果是旧版请求（包含 functions），需要转换响应格式
      const isOldRequest = req.body.functions !== undefined;
      
      if (isOldRequest) {
        // 对于旧版请求，也需要清理等待心跳
        if (waitingHeartbeat) {
          clearInterval(waitingHeartbeat);
          console.log(`[${new Date().toISOString()}] Cleared waiting heartbeat for old request format`);
        }

        // 收集完整的响应数据
        let responseData = '';
        response.data.on('data', (chunk) => {
          responseData += chunk;
        });

        response.data.on('end', () => {
          try {
            const jsonData = JSON.parse(responseData);
            // 检查是否需要转换响应
            if (shouldTransformResponse(req, jsonData.choices[0])) {
              const transformedResponse = transformNewResponse(jsonData.choices[0]);
              res.json({
                ...jsonData,
                choices: [transformedResponse]
              });
            } else {
              res.json(jsonData);
            }

            logRequest({
              gatewayKey: gatewayKeyName,
              model: model,
              body: req.body,
              status: response.status,
              clientIp
            });
          } catch (error) {
            console.error('Error transforming response:', error);
            res.status(500).json({ error: 'Error processing response' });
          }
        });
      } else {
        if (isThinkingMode) {
          // 当启用思考模式时，拦截流式响应，在第一个非空 delta.content 前面插入 </think>
          let firstDeltaSent = false;
          let buffer = '';
          if (!res.headersSent) {
            res.writeHead(response.status, response.headers);
          }
          response.data.on('data', chunk => {
            buffer += chunk.toString();
            let newlineIndex;
            // 按行处理数据（每行通常以换行符分隔）
            while ((newlineIndex = buffer.indexOf('\n')) >= 0) {
              const line = buffer.slice(0, newlineIndex);
              buffer = buffer.slice(newlineIndex + 1);
              if (line.startsWith('data: ')) {
                const content = line.substring(6).trim();
                if (content === '[DONE]') {
                  res.write(`data: [DONE]\n\n`);
                  continue;
                }
                try {
                  const jsonData = JSON.parse(content);
                  // 检查是否已经发送过含 delta.content 的数据
                  if (
                    !firstDeltaSent &&
                    jsonData.choices &&
                    Array.isArray(jsonData.choices) &&
                    jsonData.choices[0].delta &&
                    jsonData.choices[0].delta.content &&
                    jsonData.choices[0].delta.content.trim() !== ''
                  ) {
                    // 在原有 delta.content 前插入 </think>
                    jsonData.choices[0].delta.content = '</think>\n\n' + jsonData.choices[0].delta.content;
                    firstDeltaSent = true;
                  }
                  res.write(`data: ${JSON.stringify(jsonData)}\n\n`);
                } catch (err) {
                  res.write(`${line}\n\n`);
                }
              } else {
                res.write(`${line}\n\n`);
              }
            }
          });
          response.data.on('end', () => {
            logRequest({
              gatewayKey: gatewayKeyName,
              model: model,
              body: req.body,
              status: response.status,
              clientIp
            });
            res.end();
          });
        } else {
          // 为所有模型添加改进的流式处理
          if (req.body.stream !== false) {
            console.log(`[${new Date().toISOString()}] Setting up enhanced streaming for model: ${mappedModel}`);

            // 为所有模型使用改进的流式处理
            let buffer = '';
            let streamHeartbeatInterval;
            let lastDataTime = Date.now();
            let chunkCount = 0;
            let totalDataReceived = 0;

            // 设置流式心跳机制，每10秒检查一次
            streamHeartbeatInterval = setInterval(() => {
              const now = Date.now();
              const timeSinceLastData = now - lastDataTime;
              console.log(`[${new Date().toISOString()}] Stream heartbeat check for ${mappedModel} - Time since last data: ${timeSinceLastData}ms`);

              if (timeSinceLastData > 10000 && !res.writableEnded) {
                try {
                  console.log(`[${new Date().toISOString()}] Sending stream heartbeat to keep connection alive`);
                  // 发送心跳数据，保持连接活跃
                  const heartbeatData = {
                    choices: [{
                      delta: { content: "" },
                      index: 0,
                      finish_reason: null
                    }],
                    created: Math.floor(Date.now() / 1000),
                    id: `chatcmpl-stream-${Date.now()}`,
                    model: mappedModel,
                    object: "chat.completion.chunk"
                  };
                  res.write(`data: ${JSON.stringify(heartbeatData)}\n\n`);
                  lastDataTime = now;
                } catch (err) {
                  console.error(`[${new Date().toISOString()}] Error sending stream heartbeat:`, err);
                  clearInterval(streamHeartbeatInterval);
                }
              }
            }, 10000);

            // 检查响应头，确定实际使用的target
            const getTransformersForUsedTarget = (responseHeaders, config) => {
              let targetTransformers = [];
              
              // 获取Portkey告诉我们使用的具体配置索引
              const usedOptionIndex = responseHeaders['x-portkey-last-used-option-index'];
              console.log(`[${new Date().toISOString()}] Portkey used option:`, usedOptionIndex);
              
              if (usedOptionIndex) {
                try {
                  // 解析索引路径，如 'config.targets[1]' 或 'config.targets[0].targets[3]'
                  let targetConfig = null;
                  
                  if (usedOptionIndex.startsWith('config.targets[')) {
                    // 提取索引号
                    const matches = usedOptionIndex.match(/config\.targets\[(\d+)\](?:\.targets\[(\d+)\])?/);
                    if (matches) {
                      const primaryIndex = parseInt(matches[1]);
                      const secondaryIndex = matches[2] ? parseInt(matches[2]) : null;
                      
                      console.log(`[${new Date().toISOString()}] Parsed indices - primary: ${primaryIndex}, secondary: ${secondaryIndex}`);
                      
                      // 获取对应的target配置
                      if (config.targets && config.targets[primaryIndex]) {
                        if (secondaryIndex !== null) {
                          // 嵌套targets的情况
                          const primaryTarget = config.targets[primaryIndex];
                          if (primaryTarget.targets && primaryTarget.targets[secondaryIndex]) {
                            targetConfig = primaryTarget.targets[secondaryIndex];
                            console.log(`[${new Date().toISOString()}] Found nested target config:`, targetConfig);
                          }
                        } else {
                          // 直接targets的情况
                          targetConfig = config.targets[primaryIndex];
                          console.log(`[${new Date().toISOString()}] Found direct target config:`, targetConfig);
                        }
                      }
                    }
                  }
                  
                  // 检查target配置中的转换器
                  if (targetConfig) {
                    // 检查多个可能的位置
                    if (targetConfig.__trans_types) {
                      targetTransformers = targetConfig.__trans_types;
                      console.log(`[${new Date().toISOString()}] Found transformers in __trans_types:`, targetTransformers);
                    }
                    else if (targetConfig['trans-type']) {
                      targetTransformers = targetConfig['trans-type'];
                      console.log(`[${new Date().toISOString()}] Found transformers in trans-type:`, targetTransformers);
                    }
                    else if (targetConfig.added_params && targetConfig.added_params['trans-type']) {
                      targetTransformers = targetConfig.added_params['trans-type'];
                      console.log(`[${new Date().toISOString()}] Found transformers in added_params.trans-type:`, targetTransformers);
                    }
                    
                    console.log(`[${new Date().toISOString()}] Target config structure:`, JSON.stringify(targetConfig, null, 2));
                  }
                  
                } catch (error) {
                  console.error(`[${new Date().toISOString()}] Error parsing used option index:`, error);
                  console.log('Full response headers:', responseHeaders);
                }
              }
              
              // 确保返回的是有效的转换器类型
              const supportedTransformers = ['reasoning_content', 'thinking_mode', 'chain_of_thought', 'add_think_start'];
              const validTransformers = Array.isArray(targetTransformers) 
                ? targetTransformers.filter(type => supportedTransformers.includes(type))
                : [];
              
              console.log(`[${new Date().toISOString()}] Final valid transformers:`, validTransformers);
              return validTransformers;
            };

            // 对于流式请求，响应头可能已经在等待期间设置了
            if (!res.headersSent) {
              console.log(`[${new Date().toISOString()}] Writing response headers for ${mappedModel}`);
              res.writeHead(response.status, response.headers);
            } else {
              console.log(`[${new Date().toISOString()}] Response headers already sent during waiting period for ${mappedModel}`);
            }

            // 根据实际使用的target动态确定转换器
            const actualTransformerTypes = getTransformersForUsedTarget(response.headers, config);
            console.log(`[${new Date().toISOString()}] Dynamic transformer types for ${mappedModel}:`, actualTransformerTypes);

            // 初始化转换器管理器
            const transformerManager = new TransformerManager(actualTransformerTypes);
            console.log(`[${new Date().toISOString()}] Initialized transformers for ${mappedModel}: ${transformerManager.getActiveTransformerNames().join(', ')}`);

            response.data.on('data', chunk => {
              chunkCount++;
              totalDataReceived += chunk.length;
              lastDataTime = Date.now();

              console.log(`[${new Date().toISOString()}] Received chunk ${chunkCount}, size: ${chunk.length}, total: ${totalDataReceived}`);

              buffer += chunk.toString();
              let newlineIndex;
              let linesProcessed = 0;

              // 按行处理数据
              while ((newlineIndex = buffer.indexOf('\n')) >= 0) {
                const line = buffer.slice(0, newlineIndex);
                buffer = buffer.slice(newlineIndex + 1);
                linesProcessed++;

                if (line.startsWith('data: ')) {
                  const content = line.substring(6).trim();
                  if (content === '[DONE]') {
                    console.log(`[${new Date().toISOString()}] Received [DONE] signal`);
                    
                    // 在发送 [DONE] 前，处理转换器的最终内容
                    if (transformerManager.hasActiveTransformers()) {
                      const finalChunks = transformerManager.finalize();
                      finalChunks.forEach(finalChunk => {
                        finalChunk.model = mappedModel;
                        res.write(`data: ${JSON.stringify(finalChunk)}\n\n`);
                        console.log(`[${new Date().toISOString()}] Sent transformer final chunk`);
                      });
                    }
                    
                    res.write(`data: [DONE]\n\n`);
                    continue;
                  }

                  try {
                    // 解析JSON数据
                    let jsonData = JSON.parse(content);
                    
                    // 如果有活跃的转换器，应用转换
                    if (transformerManager.hasActiveTransformers()) {
                      jsonData = transformerManager.transform(jsonData);
                      console.log(`[${new Date().toISOString()}] Applied content transformers`);
                    }
                    
                    res.write(`data: ${JSON.stringify(jsonData)}\n\n`);
                    console.log(`[${new Date().toISOString()}] Forwarded processed JSON data`);
                  } catch (err) {
                    // 如果不是有效JSON，直接转发原始行
                    console.log(`[${new Date().toISOString()}] Forwarding non-JSON line: ${line.substring(0, 50)}...`);
                    res.write(`${line}\n`);
                  }
                } else {
                  res.write(`${line}\n`);
                }
              }

              console.log(`[${new Date().toISOString()}] Processed ${linesProcessed} lines from chunk ${chunkCount}`);
            });

            response.data.on('end', () => {
              const endTime = Date.now();
              const totalTime = endTime - requestStartTime;
              console.log(`[${new Date().toISOString()}] Stream ended for ${mappedModel} - Total time: ${totalTime}ms, Chunks: ${chunkCount}, Data: ${totalDataReceived} bytes`);

              if (streamHeartbeatInterval) {
                clearInterval(streamHeartbeatInterval);
                console.log(`[${new Date().toISOString()}] Cleared stream heartbeat interval`);
              }

              // 处理剩余的buffer
              if (buffer.trim()) {
                console.log(`[${new Date().toISOString()}] Writing remaining buffer: ${buffer.length} chars`);
                res.write(buffer);
              }

              logRequest({
                gatewayKey: gatewayKeyName,
                model: model,
                body: req.body,
                status: response.status,
                clientIp
              });

              console.log(`[${new Date().toISOString()}] Ending response stream`);
              res.end();
            });

            response.data.on('error', (err) => {
              const errorTime = Date.now();
              const totalTime = errorTime - requestStartTime;
              console.error(`[${new Date().toISOString()}] Stream error for ${mappedModel} after ${totalTime}ms:`, err);

              if (streamHeartbeatInterval) {
                clearInterval(streamHeartbeatInterval);
                console.log(`[${new Date().toISOString()}] Cleared stream heartbeat interval due to error`);
              }

              if (!res.writableEnded) {
                console.log(`[${new Date().toISOString()}] Ending response due to stream error`);
                res.end();
              }
            });
          } else {
            // 非流式请求的处理方式
            console.log(`[${new Date().toISOString()}] Using standard response handling for non-streaming request: ${mappedModel}`);

            if (!res.headersSent) {
              res.writeHead(response.status, response.headers);
            }
            response.data.pipe(res);

            response.data.on('end', () => {
              const endTime = Date.now();
              const totalTime = endTime - requestStartTime;
              console.log(`[${new Date().toISOString()}] Standard response ended for ${mappedModel} - Total time: ${totalTime}ms`);

              logRequest({
                gatewayKey: gatewayKeyName,
                model: model,
                body: req.body,
                status: response.status,
                clientIp
              });
            });

            response.data.on('error', (err) => {
              const errorTime = Date.now();
              const totalTime = errorTime - requestStartTime;
              console.error(`[${new Date().toISOString()}] Standard response error for ${mappedModel} after ${totalTime}ms:`, err);
            });
          }
        }
      }
    } catch (error) {
      const errorTime = Date.now();
      const totalTime = errorTime - requestStartTime;

      // 清理等待心跳
      if (typeof waitingHeartbeat !== 'undefined' && waitingHeartbeat) {
        clearInterval(waitingHeartbeat);
        console.log(`[${new Date().toISOString()}] Cleared waiting heartbeat due to error`);
      }

      // 处理请求错误
      const isTimeoutError = error.code === 'ECONNABORTED' || error.message.includes('timeout');

      console.error(`[${new Date().toISOString()}] Request error for ${mappedModel} after ${totalTime}ms:`, {
        message: error.message,
        code: error.code,
        status: error.response?.status,
        statusText: error.response?.statusText,
        isTimeoutError,
        url: url,
        model: mappedModel
      });

      // 如果有响应数据，也记录一下
      if (error.response?.data) {
        console.error(`[${new Date().toISOString()}] Error response data:`, error.response.data);
      }

      // 只在响应头未发送时才发送错误响应
      if (!res.headersSent) {
        console.log(`[${new Date().toISOString()}] Headers not sent, sending error response for ${mappedModel}`);

        if (isTimeoutError) {
          // 对于超时错误，返回更友好的错误信息
          console.log(`[${new Date().toISOString()}] Sending 524 timeout error for model: ${mappedModel}`);
          res.status(524).json({
            error: 'Gateway timeout - The model is taking longer than expected to respond. Please try again.',
            model: mappedModel,
            suggestion: 'Consider using a shorter prompt or try again later.',
            duration: totalTime
          });
        } else {
          console.log(`[${new Date().toISOString()}] Re-throwing error for non-timeout error`);
          throw error;
        }
      } else {
        console.error(`[${new Date().toISOString()}] Headers already sent, cannot send error response for ${mappedModel}`);
        console.error('Error during streaming response:', error);
        // 结束响应而不是抛出错误
        if (!res.writableEnded) {
          console.log(`[${new Date().toISOString()}] Ending response stream due to error`);
          res.end();
        }
      }
    }
  };

  try {
    await makeRequest(gatewayUrl);
  } catch (err) {
    console.error('Error requesting primary gateway', err);
    if (err.response && err.response.status === 502 && !res.headersSent) {
      console.log('Received 502, retrying with secondary gateway URL');
      gatewayUrl = `${gatewayBaseUrl2}${requestPath}`;
      try {
        await makeRequest(gatewayUrl);
      } catch (retryErr) {
        console.error('Error requesting secondary gateway', retryErr);
        if (!res.headersSent) {
          res.status(500).json({ error: 'Internal server error' });
        }
        logRequest({
          gatewayKey: gatewayKeyName,
          model: model,
          body: req.body,
          status: 500,
          clientIp
        });
      }
    } else {
      console.error('Non-502 error occurred:', err);
      if (!res.headersSent) {
        res.status(500).json({ error: 'Internal server error' });
      }
      logRequest({
        gatewayKey: gatewayKeyName,
        model: model,
        body: req.body,
        status: 500,
        clientIp
      });
    }
  }
  }
  };
}

// 动态路由处理器 - 实时读取配置文件，支持v1和v2版本
function dynamicRouteHandler(req, res, next) {
  try {
    const confContent = fs.readFileSync(confPath, 'utf8');
    const conf = JSON.parse(confContent);

    // 获取配置的路径列表，默认为 ['gateway']
    const gatewayPaths = conf.gateway_paths || ['gateway'];

    // 解析请求路径，获取路径段
    const pathSegments = req.path.split('/').filter(segment => segment !== '');
    const requestedPath = pathSegments[0];
    const versionPath = pathSegments[1]; // v1, v2 等

    // 检查请求的路径是否在允许的网关路径中
    if (gatewayPaths.includes(requestedPath)) {
      let handler;

      // 根据版本路径决定使用哪个处理器
      if (versionPath === 'v2') {
        // 使用简单版本处理器
        console.log(`[${new Date().toISOString()}] Using V2 (simple) handler for path: ${requestedPath}`);
        handler = createSimpleGatewayHandler(requestedPath);
      } else {
        // 默认使用复杂版本处理器（v1或无版本标识）
        console.log(`[${new Date().toISOString()}] Using V1 (complex) handler for path: ${requestedPath}`);
        handler = createGatewayHandler(requestedPath);
      }

      return handler(req, res, next);
    } else {
      // 路径不在允许列表中，返回 404
      return res.status(404).json({
        error: 'Path not found',
        message: `The path '/${requestedPath}' is not configured in gateway_paths. Available paths: ${gatewayPaths.join(', ')}`
      });
    }

  } catch (err) {
    console.error('Error in dynamic route handler:', err);
    // 如果配置文件读取失败，使用默认处理器
    const handler = createGatewayHandler('gateway');
    return handler(req, res, next);
  }
}

// 设置通配符路由来捕获所有请求
app.all('/*', upload.any(), dynamicRouteHandler);

const PORT = process.env.PORT || 3473;
app.listen(PORT, () => {
  console.log(`Server is running on port ${PORT}`);
});
