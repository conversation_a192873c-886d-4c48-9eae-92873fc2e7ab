const axios = require('axios');

// 测试配置
const baseUrl = 'http://localhost:3473';
const testToken = 'a881018'; // 使用配置中的测试token

// 测试函数
async function testEndpoint(path, description) {
  try {
    console.log(`\n=== 测试: ${description} ===`);
    console.log(`请求路径: ${path}`);
    
    const response = await axios.get(`${baseUrl}${path}`, {
      headers: {
        'Authorization': `Bearer ${testToken}`
      },
      timeout: 5000
    });
    
    console.log(`状态码: ${response.status}`);
    console.log(`响应数据:`, JSON.stringify(response.data, null, 2));
    return true;
  } catch (error) {
    console.log(`错误: ${error.message}`);
    if (error.response) {
      console.log(`状态码: ${error.response.status}`);
      console.log(`错误响应:`, JSON.stringify(error.response.data, null, 2));
    }
    return false;
  }
}

async function runTests() {
  console.log('开始测试网关服务器的v1和v2路线...\n');
  
  // 测试v1路线（复杂版本）
  await testEndpoint('/gateway/v1/models', 'V1路线 - 模型列表');
  
  // 测试v2路线（简单版本）
  await testEndpoint('/gateway/v2/models', 'V2路线 - 模型列表');
  
  // 测试其他路径的v1和v2
  await testEndpoint('/test/v1/models', 'Test路径V1 - 模型列表');
  await testEndpoint('/test/v2/models', 'Test路径V2 - 模型列表');
  
  // 测试无版本标识（应该使用v1逻辑）
  await testEndpoint('/gateway/models', '无版本标识 - 模型列表（应使用V1逻辑）');
  
  console.log('\n测试完成！');
}

// 运行测试
runTests().catch(console.error);
